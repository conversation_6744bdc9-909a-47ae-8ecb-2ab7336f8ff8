# 光电对抗仿真系统开发规划文档

## 1. 项目概述

### 1.1 项目目标
开发一个基于C++的光电对抗仿真系统，提供命令行接口，支持光电目标数据产生、光电干扰设备数据产生、光电对抗侦察设备数据产生和单站点分选识别等功能。

### 1.2 技术栈
- **开发语言**: C++17/20
- **开发环境**: Visual Studio 2019/2022
- **构建系统**: CMake + MSBuild
- **第三方库**:
  - JSON处理: nlohmann/json
  - 图像处理: OpenCV 4.x
  - 数学计算: Eigen3
  - 日志系统: spdlog

### 1.3 输出规格
- **图片输出**: 640×480分辨率，支持中文标注
- **视频输出**: 640×480分辨率，时长可配置
- **数据格式**: JSON格式输入输出
- **接口形式**: 单一主函数入口，命令行调用

## 2. 系统架构设计

### 2.1 整体架构
```
PhoElecSystem/
├── src/
│   ├── main.cpp                    # 主入口函数
│   ├── core/                       # 核心模块
│   │   ├── SystemManager.h/cpp     # 系统管理器
│   │   ├── ConfigParser.h/cpp      # 配置解析器
│   │   └── Logger.h/cpp            # 日志管理器
│   ├── devices/                    # 设备模块
│   │   ├── base/                   # 基础设备类
│   │   ├── target/                 # 光电目标设备
│   │   ├── jamming/                # 光电干扰设备
│   │   └── reconnaissance/         # 光电侦察设备
│   ├── simulation/                 # 仿真模块
│   │   ├── physics/                # 物理仿真
│   │   ├── imaging/                # 成像仿真
│   │   └── signal/                 # 信号处理
│   ├── algorithms/                 # 算法模块
│   │   ├── guidance/               # 制导算法
│   │   ├── detection/              # 目标检测
│   │   └── tracking/               # 目标跟踪
│   ├── output/                     # 输出模块
│   │   ├── ImageGenerator.h/cpp    # 图像生成器
│   │   ├── VideoGenerator.h/cpp    # 视频生成器
│   │   └── DataExporter.h/cpp      # 数据导出器
│   └── utils/                      # 工具模块
│       ├── MathUtils.h/cpp         # 数学工具
│       ├── FileUtils.h/cpp         # 文件工具
│       └── StringUtils.h/cpp       # 字符串工具
├── include/                        # 头文件目录
├── docs/                           # 文档目录
├── data/                           # 数据目录
├── output/                         # 输出目录
└── CMakeLists.txt                  # CMake配置文件
```

### 2.2 模块职责

#### 2.2.1 核心模块 (core/)
- **SystemManager**: 系统总控制器，协调各模块工作
- **ConfigParser**: JSON配置解析，参数验证
- **Logger**: 统一日志管理，支持多级别日志

#### 2.2.2 设备模块 (devices/)
- **base/**: 设备基类，定义通用接口
- **target/**: 光电目标设备实现（红外、激光、电视）
- **jamming/**: 光电干扰设备实现（烟幕、红外诱饵弹）
- **reconnaissance/**: 光电侦察设备实现（红外侦测、信号分析器）

#### 2.2.3 仿真模块 (simulation/)
- **physics/**: 物理仿真引擎（大气传输、辐射传播）
- **imaging/**: 成像仿真（透视投影、光学系统）
- **signal/**: 信号处理仿真（调制解调、滤波）

#### 2.2.4 算法模块 (algorithms/)
- **guidance/**: 制导算法（红外制导、激光制导、电视制导）
- **detection/**: 目标检测算法（图像识别、特征提取）
- **tracking/**: 目标跟踪算法（卡尔曼滤波、粒子滤波）

#### 2.2.5 输出模块 (output/)
- **ImageGenerator**: 图像生成和中文标注
- **VideoGenerator**: 视频生成和编码
- **DataExporter**: 数据文件导出

## 3. 接口设计

### 3.1 主函数接口
```cpp
int main(int argc, char* argv[]);
```

### 3.2 核心处理函数
```cpp
std::string ProcessPhoElecSimulation(const std::string& jsonInput);
```

### 3.3 输入JSON格式
```json
{
    "task_type": "target_data_generation",
    "scenario": {
        "environment": {
            "atmosphere": {...},
            "lighting": {...}
        },
        "devices": {
            "target_devices": [...],
            "jamming_devices": [...],
            "reconnaissance_devices": [...]
        }
    },
    "simulation_params": {
        "duration": 10.0,
        "time_step": 0.1,
        "output_format": ["images", "video", "data"]
    },
    "output_config": {
        "image_resolution": "640x480",
        "video_fps": 30,
        "data_format": "json"
    }
}
```

### 3.4 输出JSON格式
```json
{
    "status": "success",
    "execution_time": 1.23,
    "results": {
        "generated_files": {
            "images": ["output/image_001.png", ...],
            "videos": ["output/video_001.mp4"],
            "data": ["output/data_001.json"]
        },
        "statistics": {
            "detection_accuracy": 0.95,
            "tracking_precision": 0.87,
            "interference_level": 0.23
        },
        "performance_metrics": {
            "detection_range": 15000.0,
            "detection_probability": 0.92,
            "identification_accuracy": 0.88
        }
    },
    "logs": [
        {"level": "INFO", "message": "仿真开始", "timestamp": "2024-01-01T10:00:00"},
        {"level": "INFO", "message": "目标检测完成", "timestamp": "2024-01-01T10:00:01"}
    ]
}
```

## 4. 物理模型与算法实现

### 4.1 红外制导仿真
- **目标红外辐射**: 基于斯蒂芬-玻尔兹曼定律 E = εσT⁴
- **大气传输**: 基于Beer-Lambert定律 I = I₀e^(-τ)
- **导引头建模**: 调制盘空间滤波 V_out(t) = V_in(t) · M(θ(t))
- **跟踪精度**: 均方误差评估 MSE = E[(θ_est - θ_true)²]
- **制导律**: 比例导引法 λ̇ = k · θ̇

### 4.2 激光制导仿真
- **激光辐射源**: 高斯光束模型 I(r) = I₀e^(-2r²/w²)
- **目标反射**: 朗伯反射定律 R = ρ · I_in · cos θ
- **大气传输**: 大气湍流模型
- **光电转换**: 光电效应方程 I_out = η · q · P
- **信号处理**: PID控制算法

### 4.3 电视制导仿真
- **成像模型**: 透视投影 x = f·X/Z, y = f·Y/Z
- **目标检测**: 模板匹配相关系数计算
- **控制策略**: Bang-Bang控制
- **飞行仿真**: 六自由度动力学模型

## 5. 开发计划

### 5.1 第一阶段：基础架构搭建
1. 项目结构创建和CMake配置
2. 核心模块开发（SystemManager, ConfigParser, Logger）
3. 基础设备类设计和实现
4. JSON输入输出接口实现

### 5.2 第二阶段：设备模块开发
1. 光电目标设备实现（红外、激光、电视传感器）
2. 光电干扰设备实现（烟幕、红外诱饵弹）
3. 光电侦察设备实现（红外侦测、信号分析器）
4. 设备参数配置和验证

### 5.3 第三阶段：仿真引擎开发
1. 物理仿真引擎（大气传输、辐射传播）
2. 成像仿真系统（光学系统、透视投影）
3. 信号处理仿真（调制解调、滤波算法）
4. 数学模型验证和优化

### 5.4 第四阶段：算法模块开发
1. 制导算法实现（红外、激光、电视制导）
2. 目标检测算法（图像识别、特征提取）
3. 目标跟踪算法（卡尔曼滤波、粒子滤波）
4. 算法性能优化

### 5.5 第五阶段：输出模块开发
1. 图像生成器（640×480分辨率，中文支持）
2. 视频生成器（MP4编码，可配置时长）
3. 数据导出器（JSON格式，统计信息）
4. 性能优化